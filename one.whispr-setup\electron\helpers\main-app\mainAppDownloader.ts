import { app } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { rename } from 'fs';
import { getLauncherWindow } from '../../window';
import { VersionChecker } from './versionChecker';
import { FileDownloader } from './fileDownloader';
import { setupMainAppIpc } from './ipc';
import { microsoftStoreHandler } from '../microsoft/microsoftStoreHandler';
import { IS_MICROSOFT } from '../../../src/lib/constants';

interface DownloadManifest {
  version: string;
  files: ManifestFile[];
  totalSize: number;
}

interface ManifestFile {
  path: string;
  size: number;
  checksum: string;
  url: string;
}

/**
 * Main app downloader orchestrator
 */
export class MainAppDownloader {
  private manifest: DownloadManifest | null = null;
  private downloadPath: string;
  private isDownloading: boolean = false;
  private abortController: AbortController | null = null;
  private versionChecker: VersionChecker;
  private fileDownloader: FileDownloader;

  
  constructor() {
    // Set download path - use custom path if specified in environment, otherwise use userData/MainApp
    if (process.env.DOWNLOAD_PATH) {
      // Use custom download path (relative to process.cwd())
      this.downloadPath = path.resolve(process.cwd(), process.env.DOWNLOAD_PATH);
      console.log(`[DOWNLOADER] Using custom download path: ${this.downloadPath}`);
    } else {
      // Default to userData/MainApp
      this.downloadPath = path.join(app.getPath('userData'), 'MainApp');
      console.log(`[DOWNLOADER] Using default download path: ${this.downloadPath}`);
    }

    // Create download directory if it doesn't exist
    fs.ensureDirSync(this.downloadPath);

    // Initialize components
    this.versionChecker = new VersionChecker(this.downloadPath);
    this.fileDownloader = new FileDownloader(this.downloadPath);
    setupMainAppIpc(this);
  }

  /**
   * Check if download is needed
   */
  async checkDownloadNeeded(): Promise<{ needed: boolean, reason: string }> {
    return this.versionChecker.checkDownloadNeeded();
  }

  /**
   * Get the current manifest
   */
  getManifest(): DownloadManifest | null {
    return this.manifest;
  }

  /**
   * Start downloading files from the manifest (or copying for Microsoft Store)
   */
  async startDownload(): Promise<boolean> {
    if (this.isDownloading) {
      console.log('[DOWNLOADER] Download already in progress');
      return false;
    }

    // Check if this is a Microsoft Store build
    if (IS_MICROSOFT) {
      console.log('[DOWNLOADER] Microsoft Store build detected - using copy instead of download');
      return this.startMicrosoftStoreCopy();
    }

    try {
      this.isDownloading = true;
      this.abortController = new AbortController();
      this.fileDownloader.setAbortController(this.abortController);
      
      // Fetch manifest if not already fetched
      if (!this.manifest) {
        this.manifest = await this.versionChecker.fetchManifest();
        
        if (!this.manifest) {
          throw new Error('Failed to fetch manifest');
        }
      }
      
      const totalFiles = this.manifest.files.length;
      
      // Download each file
      for (let i = 0; i < totalFiles; i++) {
        const file = this.manifest.files[i];

        // Handle ASAR files specially - download as .tmp and rename after verification
        const isAsarFile = file.path.endsWith('.asar');

        // Use proper directory structure for all files
        const properPath = file.path.replace(/^\.\//, '');
        const finalPath = path.join(this.downloadPath, properPath);
        const downloadPath = isAsarFile ? finalPath + '.tmp' : finalPath;

        // Ensure directory exists
        await fs.ensureDir(path.dirname(downloadPath));

        // Download the file (to .tmp for ASAR files)
        await this.fileDownloader.downloadFile(file, i + 1, totalFiles, downloadPath);

        // Verify checksum
        const isValid = await this.fileDownloader.verifyChecksum(downloadPath, file.checksum);

        if (!isValid) {
          throw new Error(`Checksum verification failed for ${file.path}`);
        }

        // Rename ASAR files from .tmp to final name after successful verification
        if (isAsarFile) {
          // Use Node.js fs.rename instead of fs-extra's move to avoid package validation
          await new Promise<void>((resolve, reject) => {
            rename(downloadPath, finalPath, (err: any) => {
              if (err) reject(err);
              else resolve();
            });
          });
          console.log(`[DOWNLOADER] Renamed ${downloadPath} to ${finalPath}`);
        }
      }
      
      // Fetch server version to get releaseDate
      const serverVersion = await this.versionChecker.fetchServerVersion();

      // Save version information with releaseDate
      await fs.writeJson(path.join(this.downloadPath, 'version.json'), {
        version: this.manifest.version,
        releaseDate: serverVersion?.releaseDate,
        installedAt: new Date().toISOString()
      });

      this.isDownloading = false;
      this.abortController = null;
      
      // Notify renderer that download is complete
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('download:complete', {
          version: this.manifest.version
        });
      }
      
      return true;
    } catch (error) {
      console.error('[DOWNLOADER] Download error:', error);
      
      this.isDownloading = false;
      this.abortController = null;
      
      // Notify renderer of error
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('download:error', {
          message: error instanceof Error ? error.message : String(error)
        });
      }
      
      return false;
    }
  }

  /**
   * Cancel the current download
   */
  cancelDownload(): boolean {
    if (!this.isDownloading || !this.abortController) {
      return false;
    }
    
    console.log('[DOWNLOADER] Cancelling download');
    
    this.abortController.abort();
    this.isDownloading = false;
    this.abortController = null;
    
    return true;
  }

  /**
   * Get the download path
   */
  getDownloadPath(): string {
    return this.downloadPath;
  }

  /**
   * Check if a download is in progress
   */
  isDownloadInProgress(): boolean {
    return this.isDownloading;
  }

  /**
   * Microsoft Store copy method - replaces download with copy from embedded resources
   */
  private async startMicrosoftStoreCopy(): Promise<boolean> {
    try {
      this.isDownloading = true;
      console.log('[DOWNLOADER] Starting Microsoft Store MainApp copy...');

      // Send initial progress
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('download:progress', {
          progress: 0,
          speed: 0,
          eta: 0,
          downloaded: 0,
          total: 100
        });
      }

      // Copy MainApp from embedded resources to AppData
      const success = await microsoftStoreHandler.copyMainAppToAppData((progress) => {
        if (launcherWindow) {
          launcherWindow.webContents.send('download:progress', {
            progress,
            speed: 0,
            eta: 0,
            downloaded: progress,
            total: 100
          });
        }
      });

      if (success) {
        console.log('[DOWNLOADER] Microsoft Store MainApp copy completed successfully');

        // Send completion
        if (launcherWindow) {
          launcherWindow.webContents.send('download:progress', {
            progress: 100,
            speed: 0,
            eta: 0,
            downloaded: 100,
            total: 100
          });
          launcherWindow.webContents.send('download:complete');
        }

        return true;
      } else {
        throw new Error('Microsoft Store MainApp copy failed');
      }
    } catch (error) {
      console.error('[DOWNLOADER] Microsoft Store copy error:', error);

      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('download:error', {
          message: error instanceof Error ? error.message : 'Microsoft Store copy failed'
        });
      }

      return false;
    } finally {
      this.isDownloading = false;
    }
  }
}

// Export singleton instance
export const downloader = new MainAppDownloader();
